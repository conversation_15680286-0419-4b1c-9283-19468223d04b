<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 执行器测试</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        .test-case {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .test-title {
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 0.5rem;
        }
        .test-code {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.375rem;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 0.5rem 0;
        }
        .test-expected {
            background: #ecfdf5;
            border: 1px solid #10b981;
            padding: 0.5rem;
            border-radius: 0.375rem;
            margin: 0.5rem 0;
        }
        .status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: bold;
        }
        .status.pass { background: #10b981; color: white; }
        .status.fail { background: #ef4444; color: white; }
        .status.manual { background: #f59e0b; color: white; }
    </style>
</head>
<body>
    <h1>JavaScript 执行器功能测试</h1>
    <p>以下是一些测试用例，可以在 JavaScript 执行器中运行来验证功能：</p>

    <div class="test-case">
        <div class="test-title">测试 1: 基本输出</div>
        <div class="test-code">console.log('Hello, World!');
console.log('测试中文输出');
'返回值测试';</div>
        <div class="test-expected">
            <strong>期望输出:</strong><br>
            控制台输出:<br>
            [LOG] Hello, World!<br>
            [LOG] 测试中文输出<br><br>
            返回值:<br>
            返回值测试
        </div>
        <span class="status manual">手动测试</span>
    </div>

    <div class="test-case">
        <div class="test-title">测试 2: 数学计算</div>
        <div class="test-code">function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log('斐波那契数列前5项:');
for (let i = 0; i < 5; i++) {
  console.log(`F(${i}) = ${fibonacci(i)}`);
}

fibonacci(10);</div>
        <div class="test-expected">
            <strong>期望输出:</strong><br>
            控制台输出显示斐波那契数列，返回值为 55
        </div>
        <span class="status manual">手动测试</span>
    </div>

    <div class="test-case">
        <div class="test-title">测试 3: 数组操作</div>
        <div class="test-code">const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(x => x * 2);
const sum = numbers.reduce((a, b) => a + b, 0);

console.log('原数组:', numbers);
console.log('翻倍后:', doubled);
console.log('求和:', sum);

{ original: numbers, doubled, sum };</div>
        <div class="test-expected">
            <strong>期望输出:</strong><br>
            控制台输出显示数组操作结果，返回值为包含三个属性的对象
        </div>
        <span class="status manual">手动测试</span>
    </div>

    <div class="test-case">
        <div class="test-title">测试 4: 错误处理</div>
        <div class="test-code">console.log('开始测试错误处理');

try {
  throw new Error('这是一个测试错误');
} catch (error) {
  console.error('捕获到错误:', error.message);
}

console.log('错误处理完成');

// 这行会产生语法错误（取消注释测试）
// undefinedFunction();</div>
        <div class="test-expected">
            <strong>期望输出:</strong><br>
            控制台输出显示错误被正确捕获和处理
        </div>
        <span class="status manual">手动测试</span>
    </div>

    <div class="test-case">
        <div class="test-title">测试 5: 对象和类</div>
        <div class="test-code">class Calculator {
  constructor() {
    this.result = 0;
  }
  
  add(num) {
    this.result += num;
    return this;
  }
  
  multiply(num) {
    this.result *= num;
    return this;
  }
  
  getResult() {
    return this.result;
  }
}

const calc = new Calculator();
const result = calc.add(5).multiply(3).add(2).getResult();

console.log('计算结果:', result);
console.log('计算器对象:', calc);

result;</div>
        <div class="test-expected">
            <strong>期望输出:</strong><br>
            控制台输出显示计算过程，返回值为 17
        </div>
        <span class="status manual">手动测试</span>
    </div>

    <div class="test-case">
        <div class="test-title">测试 6: JSON 操作</div>
        <div class="test-code">const data = {
  name: 'JavaScript 执行器',
  version: '1.0.0',
  features: ['代码执行', '错误处理', '控制台输出'],
  config: {
    timeout: 5000,
    sandbox: true
  }
};

console.log('原始数据:', data);

const jsonString = JSON.stringify(data, null, 2);
console.log('JSON 字符串:');
console.log(jsonString);

const parsed = JSON.parse(jsonString);
console.log('解析后的数据:', parsed);

parsed.features.length;</div>
        <div class="test-expected">
            <strong>期望输出:</strong><br>
            控制台输出显示 JSON 序列化和反序列化过程，返回值为 3
        </div>
        <span class="status manual">手动测试</span>
    </div>

    <h2>测试说明</h2>
    <ol>
        <li>访问 <a href="http://localhost:3201/tools/js-executor" target="_blank">JavaScript 执行器页面</a></li>
        <li>将上述测试代码复制到代码编辑器中</li>
        <li>点击"执行代码"按钮</li>
        <li>检查输出结果是否符合期望</li>
        <li>验证错误处理是否正常工作</li>
        <li>测试响应式设计（调整浏览器窗口大小）</li>
    </ol>

    <h2>功能检查清单</h2>
    <ul>
        <li>✅ 页面正常加载</li>
        <li>✅ 代码编辑器可以输入代码</li>
        <li>✅ 执行按钮功能正常</li>
        <li>✅ 控制台输出正确显示</li>
        <li>✅ 返回值正确显示</li>
        <li>✅ 错误处理正常工作</li>
        <li>✅ 示例代码按钮功能正常</li>
        <li>✅ 清空代码按钮功能正常</li>
        <li>✅ 执行时间显示正常</li>
        <li>✅ 响应式设计正常</li>
        <li>✅ SEO 元数据正确</li>
        <li>✅ 功能说明完整</li>
    </ul>

    <script>
        // 自动化测试脚本（在浏览器控制台中运行）
        function runAutomatedTests() {
            console.log('开始自动化测试...');
            
            // 测试基本功能
            const testCases = [
                {
                    name: '基本输出测试',
                    code: 'console.log("测试"); "返回值";',
                    expectedConsole: ['[LOG] 测试'],
                    expectedReturn: '返回值'
                },
                {
                    name: '数学计算测试',
                    code: 'const result = 2 + 3 * 4; console.log("计算结果:", result); result;',
                    expectedConsole: ['[LOG] 计算结果: 14'],
                    expectedReturn: 14
                }
            ];
            
            console.log('测试用例准备完成，请在 JavaScript 执行器中手动运行。');
            return testCases;
        }
        
        // 在控制台中运行 runAutomatedTests() 来获取测试用例
    </script>
</body>
</html>
